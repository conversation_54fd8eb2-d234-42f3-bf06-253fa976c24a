package com.talkweb.ai.indexer.cli;

import com.talkweb.ai.indexer.cli.util.ConsoleColors;
import com.talkweb.ai.indexer.cli.util.ConsoleLogger;
import com.talkweb.ai.indexer.web.WebServerApplication;
import org.springframework.boot.SpringApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.stereotype.Component;
import picocli.CommandLine.Command;
import picocli.CommandLine.Option;
import picocli.CommandLine.ParentCommand;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Callable;

@Component
@Command(
    name = "server",
    description = "Run the application as a web server with REST API and Web UI",
    mixinStandardHelpOptions = true
)
public class ServerCommand implements Callable<Integer> {

    @ParentCommand
    private DocConverterCommand parent;

    @Option(
        names = {"-p", "--port"},
        description = "Server port (default: ${DEFAULT-VALUE})",
        defaultValue = "8080"
    )
    private int port;

    @Option(
        names = {"--context-path"},
        description = "Server context path (default: ${DEFAULT-VALUE})",
        defaultValue = "/doc-converter"
    )
    private String contextPath;

    @Option(
        names = {"--max-file-size"},
        description = "Maximum file upload size in MB (default: ${DEFAULT-VALUE})",
        defaultValue = "100"
    )
    private int maxFileSize;

    @Option(
        names = {"--max-concurrent-tasks"},
        description = "Maximum concurrent conversion tasks (default: ${DEFAULT-VALUE})",
        defaultValue = "10"
    )
    private int maxConcurrentTasks;

    private final ConsoleLogger logger = new ConsoleLogger();

    @Override
    public Integer call() throws Exception {
        logger.info("Starting Document Converter Web Server...");

        try {
            // Create Spring application with web profile
            SpringApplication app = new SpringApplication(WebServerApplication.class);
            app.setAdditionalProfiles("server");

            // Configure server properties programmatically
            Map<String, Object> properties = new HashMap<>();
            properties.put("server.port", port);
            properties.put("server.servlet.context-path", contextPath);
            properties.put("spring.servlet.multipart.max-file-size", maxFileSize + "MB");
            properties.put("spring.servlet.multipart.max-request-size", maxFileSize + "MB");
            properties.put("doc-converter.web.max-concurrent-tasks", maxConcurrentTasks);

            // Set temp directories based on parent command options
            if (parent != null) {
                properties.put("doc-converter.web.upload-dir", parent.getTempDir().resolve("uploads").toString());
                properties.put("doc-converter.web.result-dir", parent.getTempDir().resolve("results").toString());
                properties.put("doc-converter.plugins.directory", parent.getPluginsDir().toString());
            }

            app.setDefaultProperties(properties);

            // Start the web server
            ConfigurableApplicationContext context = app.run();

            logger.success("Document Converter Web Server started successfully!");
            logger.info("Server URL: %shttp://localhost:%d%s%s",
                ConsoleColors.CYAN, port, contextPath, ConsoleColors.RESET);
            logger.info("API Documentation: %shttp://localhost:%d%s/swagger-ui.html%s",
                ConsoleColors.CYAN, port, contextPath, ConsoleColors.RESET);
            logger.info("Press Ctrl+C to stop the server");

            // Add shutdown hook for graceful shutdown
            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                logger.info("Shutting down Document Converter Web Server...");
                context.close();
                logger.success("Server stopped successfully.");
            }));

            // Keep the server running
            Thread.currentThread().join();

        } catch (Exception e) {
            logger.error("Failed to start web server: %s", e.getMessage());
            if (parent != null && parent.isVerbose()) {
                e.printStackTrace();
            }
            return 1;
        }

        return 0;
    }
}
