package com.talkweb.ai.indexer.web.controller;

import com.talkweb.ai.indexer.web.dto.ConversionRequest;
import com.talkweb.ai.indexer.web.dto.TaskResponse;
import com.talkweb.ai.indexer.web.service.DocumentConversionService;
import com.talkweb.ai.indexer.web.service.FileStorageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * REST Controller for file management operations
 */
@RestController
@RequestMapping("/api/v1/files")
@Profile("server")
@Tag(name = "Files", description = "File upload and download operations")
public class FileController {

    private static final Logger logger = LoggerFactory.getLogger(FileController.class);

    private final DocumentConversionService conversionService;
    private final FileStorageService fileStorageService;

    @Autowired
    public FileController(
            DocumentConversionService conversionService,
            FileStorageService fileStorageService) {
        this.conversionService = conversionService;
        this.fileStorageService = fileStorageService;
    }

    @Operation(summary = "Upload file for conversion", 
               description = "Upload a document file and start conversion process")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "File uploaded and conversion started",
                    content = @Content(schema = @Schema(implementation = TaskResponse.class))),
        @ApiResponse(responseCode = "400", description = "Invalid file or validation failed"),
        @ApiResponse(responseCode = "413", description = "File too large"),
        @ApiResponse(responseCode = "415", description = "Unsupported file type")
    })
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<TaskResponse> uploadFile(
            @Parameter(description = "File to upload", required = true)
            @RequestParam("file") MultipartFile file,
            
            @Parameter(description = "Target file name (optional)")
            @RequestParam(required = false) String targetFileName,
            
            @Parameter(description = "Enable AI processing")
            @RequestParam(defaultValue = "false") Boolean enableAi,
            
            @Parameter(description = "Preserve document structure")
            @RequestParam(defaultValue = "true") Boolean preserveStructure,
            
            @Parameter(description = "Extract images")
            @RequestParam(defaultValue = "true") Boolean extractImages,
            
            @Parameter(description = "Extract metadata")
            @RequestParam(defaultValue = "true") Boolean extractMetadata) {
        
        logger.info("Uploading file: {}", file.getOriginalFilename());
        
        // Create conversion request
        ConversionRequest request = new ConversionRequest();
        request.setFile(file);
        request.setTargetFileName(targetFileName);
        request.setEnableAi(enableAi);
        request.setPreserveStructure(preserveStructure);
        request.setExtractImages(extractImages);
        request.setExtractMetadata(extractMetadata);
        
        TaskResponse response = conversionService.submitConversion(request);
        
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @Operation(summary = "Download converted file", 
               description = "Download the converted markdown file for a completed task")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "File downloaded successfully"),
        @ApiResponse(responseCode = "404", description = "Task or file not found"),
        @ApiResponse(responseCode = "400", description = "Task not completed or file not available")
    })
    @GetMapping("/download/{taskId}")
    public ResponseEntity<Resource> downloadFile(
            @Parameter(description = "Task ID") 
            @PathVariable String taskId,
            
            HttpServletRequest request) {
        
        logger.info("Downloading file for task: {}", taskId);
        
        try {
            // Get download information
            DocumentConversionService.FileDownloadInfo downloadInfo = 
                conversionService.getDownloadInfo(taskId);
            
            // Load file as resource
            Resource resource = fileStorageService.loadFileAsResource(taskId, downloadInfo.getFileName());
            
            // Determine content type
            String contentType = downloadInfo.getContentType();
            if (contentType == null) {
                contentType = "application/octet-stream";
            }
            
            // Encode filename for proper handling of special characters
            String encodedFileName = URLEncoder.encode(downloadInfo.getFileName(), StandardCharsets.UTF_8)
                .replaceAll("\\+", "%20");
            
            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(contentType))
                    .header(HttpHeaders.CONTENT_DISPOSITION, 
                           "attachment; filename=\"" + downloadInfo.getFileName() + "\"; filename*=UTF-8''" + encodedFileName)
                    .header(HttpHeaders.CONTENT_LENGTH, String.valueOf(downloadInfo.getFileSize()))
                    .body(resource);
                    
        } catch (Exception e) {
            logger.error("Error downloading file for task {}: {}", taskId, e.getMessage(), e);
            throw e;
        }
    }

    @Operation(summary = "Download file by direct path", 
               description = "Download a file using task ID and file name")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "File downloaded successfully"),
        @ApiResponse(responseCode = "404", description = "File not found"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @GetMapping("/download/{taskId}/{fileName}")
    public ResponseEntity<Resource> downloadFileByName(
            @Parameter(description = "Task ID") 
            @PathVariable String taskId,
            
            @Parameter(description = "File name") 
            @PathVariable String fileName,
            
            HttpServletRequest request) {
        
        logger.info("Downloading file {} for task: {}", fileName, taskId);
        
        try {
            // Load file as resource
            Resource resource = fileStorageService.loadFileAsResource(taskId, fileName);
            
            // Get file info
            FileStorageService.FileInfo fileInfo = fileStorageService.getFileInfo(taskId, fileName);
            
            if (fileInfo == null) {
                return ResponseEntity.notFound().build();
            }
            
            // Determine content type based on file extension
            String contentType = determineContentType(fileName);
            
            // Encode filename for proper handling of special characters
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8)
                .replaceAll("\\+", "%20");
            
            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(contentType))
                    .header(HttpHeaders.CONTENT_DISPOSITION, 
                           "attachment; filename=\"" + fileName + "\"; filename*=UTF-8''" + encodedFileName)
                    .header(HttpHeaders.CONTENT_LENGTH, String.valueOf(fileInfo.getFileSize()))
                    .body(resource);
                    
        } catch (Exception e) {
            logger.error("Error downloading file {} for task {}: {}", fileName, taskId, e.getMessage(), e);
            throw e;
        }
    }

    @Operation(summary = "Get file information", 
               description = "Get metadata about a file without downloading it")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "File information retrieved successfully"),
        @ApiResponse(responseCode = "404", description = "File not found")
    })
    @GetMapping("/info/{taskId}/{fileName}")
    public ResponseEntity<FileInfoResponse> getFileInfo(
            @Parameter(description = "Task ID") 
            @PathVariable String taskId,
            
            @Parameter(description = "File name") 
            @PathVariable String fileName) {
        
        logger.debug("Getting file info for {} in task: {}", fileName, taskId);
        
        FileStorageService.FileInfo fileInfo = fileStorageService.getFileInfo(taskId, fileName);
        
        if (fileInfo == null) {
            return ResponseEntity.notFound().build();
        }
        
        FileInfoResponse response = new FileInfoResponse(
            fileInfo.getFileName(),
            fileInfo.getFileSize(),
            fileInfo.getLastModified(),
            determineContentType(fileName)
        );
        
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Check file existence", 
               description = "Check if a file exists for a given task")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "File exists"),
        @ApiResponse(responseCode = "404", description = "File not found")
    })
    @RequestMapping(value = "/download/{taskId}/{fileName}", method = RequestMethod.HEAD)
    public ResponseEntity<Void> checkFileExists(
            @Parameter(description = "Task ID") 
            @PathVariable String taskId,
            
            @Parameter(description = "File name") 
            @PathVariable String fileName) {
        
        logger.debug("Checking existence of file {} for task: {}", fileName, taskId);
        
        FileStorageService.FileInfo fileInfo = fileStorageService.getFileInfo(taskId, fileName);
        
        if (fileInfo == null) {
            return ResponseEntity.notFound().build();
        }
        
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_LENGTH, String.valueOf(fileInfo.getFileSize()))
                .header(HttpHeaders.LAST_MODIFIED, fileInfo.getLastModified().toString())
                .build();
    }

    // Helper methods
    private String determineContentType(String fileName) {
        String extension = getFileExtension(fileName).toLowerCase();
        
        return switch (extension) {
            case "md" -> "text/markdown";
            case "txt" -> "text/plain";
            case "html", "htm" -> "text/html";
            case "pdf" -> "application/pdf";
            case "docx" -> "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            case "doc" -> "application/msword";
            case "xlsx" -> "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            case "xls" -> "application/vnd.ms-excel";
            case "pptx" -> "application/vnd.openxmlformats-officedocument.presentationml.presentation";
            case "ppt" -> "application/vnd.ms-powerpoint";
            case "png" -> "image/png";
            case "jpg", "jpeg" -> "image/jpeg";
            case "gif" -> "image/gif";
            case "tiff", "tif" -> "image/tiff";
            case "bmp" -> "image/bmp";
            default -> "application/octet-stream";
        };
    }

    private String getFileExtension(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        return lastDotIndex > 0 ? fileName.substring(lastDotIndex + 1) : "";
    }

    /**
     * Response DTO for file information
     */
    public static class FileInfoResponse {
        private final String fileName;
        private final long fileSize;
        private final java.time.LocalDateTime lastModified;
        private final String contentType;

        public FileInfoResponse(String fileName, long fileSize, 
                              java.time.LocalDateTime lastModified, String contentType) {
            this.fileName = fileName;
            this.fileSize = fileSize;
            this.lastModified = lastModified;
            this.contentType = contentType;
        }

        public String getFileName() { return fileName; }
        public long getFileSize() { return fileSize; }
        public java.time.LocalDateTime getLastModified() { return lastModified; }
        public String getContentType() { return contentType; }
    }
}
