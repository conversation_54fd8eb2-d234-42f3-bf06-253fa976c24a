package com.talkweb.ai.indexer.web.validation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Validator for file type validation
 */
public class FileTypeValidator implements ConstraintValidator<ValidFileType, MultipartFile> {

    private Set<String> allowedExtensions;
    private Set<String> blockedExtensions;
    private long maxSize;

    @Override
    public void initialize(ValidFileType constraintAnnotation) {
        this.allowedExtensions = Arrays.stream(constraintAnnotation.allowed())
                .map(String::toLowerCase)
                .collect(Collectors.toSet());
        
        this.blockedExtensions = Arrays.stream(constraintAnnotation.blocked())
                .map(String::toLowerCase)
                .collect(Collectors.toSet());
        
        this.maxSize = constraintAnnotation.maxSize();
    }

    @Override
    public boolean isValid(MultipartFile file, ConstraintValidatorContext context) {
        if (file == null || file.isEmpty()) {
            addConstraintViolation(context, "File is required and cannot be empty");
            return false;
        }

        // Check file size
        if (file.getSize() > maxSize) {
            addConstraintViolation(context, 
                String.format("File size (%d bytes) exceeds maximum allowed size (%d bytes)", 
                    file.getSize(), maxSize));
            return false;
        }

        // Get file extension
        String filename = file.getOriginalFilename();
        if (filename == null || filename.trim().isEmpty()) {
            addConstraintViolation(context, "File name is required");
            return false;
        }

        String extension = getFileExtension(filename).toLowerCase();
        
        if (extension.isEmpty()) {
            addConstraintViolation(context, "File must have an extension");
            return false;
        }

        // Check blocked extensions
        if (blockedExtensions.contains(extension)) {
            addConstraintViolation(context, "File type not allowed: " + extension);
            return false;
        }

        // Check allowed extensions
        if (!allowedExtensions.contains(extension)) {
            addConstraintViolation(context, "Unsupported file type: " + extension);
            return false;
        }

        return true;
    }

    private String getFileExtension(String filename) {
        int lastDotIndex = filename.lastIndexOf('.');
        return lastDotIndex > 0 ? filename.substring(lastDotIndex + 1) : "";
    }

    private void addConstraintViolation(ConstraintValidatorContext context, String message) {
        context.disableDefaultConstraintViolation();
        context.buildConstraintViolationWithTemplate(message).addConstraintViolation();
    }
}
