package com.talkweb.ai.indexer.web.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.talkweb.ai.indexer.web.dto.ConversionRequest;
import com.talkweb.ai.indexer.web.dto.TaskResponse;
import com.talkweb.ai.indexer.web.exception.ConversionException;
import com.talkweb.ai.indexer.web.model.ConversionTask;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Main service for document conversion operations
 */
@Service
@Profile("server")
public class DocumentConversionService {

    private static final Logger logger = LoggerFactory.getLogger(DocumentConversionService.class);

    private final ConversionTaskService taskService;
    private final FileStorageService fileStorageService;
    private final FileValidationService validationService;
    private final AsyncConversionService asyncConversionService;
    private final ObjectMapper objectMapper;

    @Autowired
    public DocumentConversionService(
            ConversionTaskService taskService,
            FileStorageService fileStorageService,
            FileValidationService validationService,
            AsyncConversionService asyncConversionService,
            ObjectMapper objectMapper) {
        this.taskService = taskService;
        this.fileStorageService = fileStorageService;
        this.validationService = validationService;
        this.asyncConversionService = asyncConversionService;
        this.objectMapper = objectMapper;
    }

    /**
     * Submit a document for conversion
     */
    public TaskResponse submitConversion(ConversionRequest request) {
        MultipartFile file = request.getFile();
        
        logger.info("Submitting conversion request for file: {}", file.getOriginalFilename());
        
        try {
            // Validate the file
            FileValidationService.ValidationResult validationResult = validationService.validateFile(file);
            
            if (!validationResult.isValid()) {
                throw new ConversionException("File validation failed: " + 
                    String.join(", ", validationResult.getErrors()));
            }
            
            // Log warnings if any
            if (!validationResult.getWarnings().isEmpty()) {
                logger.warn("File validation warnings for {}: {}", 
                    file.getOriginalFilename(), String.join(", ", validationResult.getWarnings()));
            }
            
            // Create conversion task
            ConversionTask task = createConversionTask(file, request, validationResult);
            
            // Store the uploaded file
            FileStorageService.StoredFile storedFile = fileStorageService.storeFile(file, task.getTaskId());
            
            // Update task with stored file information
            task.setFilePath(storedFile.getFilePath());
            task.setFileSize(storedFile.getFileSize());
            task.setFileType(storedFile.getFileExtension());
            
            // Save the updated task
            task = taskService.createTask(
                storedFile.getOriginalFileName(),
                storedFile.getFilePath(),
                storedFile.getFileSize(),
                storedFile.getFileExtension(),
                request.getTargetFileName(),
                serializeConversionOptions(request)
            );
            
            // Start async conversion
            CompletableFuture<ConversionTask> conversionFuture = 
                asyncConversionService.convertDocumentAsync(task.getTaskId());
            
            // Handle completion/failure asynchronously
            conversionFuture.whenComplete((completedTask, throwable) -> {
                if (throwable != null) {
                    logger.error("Async conversion failed for task {}: {}", 
                        task.getTaskId(), throwable.getMessage());
                } else {
                    logger.info("Async conversion completed for task {}", task.getTaskId());
                }
            });
            
            logger.info("Created conversion task {} for file {}", task.getTaskId(), file.getOriginalFilename());
            
            return TaskResponse.from(task);
            
        } catch (Exception e) {
            logger.error("Failed to submit conversion for file {}: {}", 
                file.getOriginalFilename(), e.getMessage(), e);
            throw new ConversionException("Failed to submit conversion: " + e.getMessage(), e);
        }
    }

    /**
     * Get conversion task status
     */
    public TaskResponse getTaskStatus(String taskId) {
        return taskService.getTaskResponse(taskId);
    }

    /**
     * Cancel a conversion task
     */
    public TaskResponse cancelConversion(String taskId) {
        logger.info("Cancelling conversion task: {}", taskId);
        
        try {
            // Try to cancel the async task first
            boolean cancelled = asyncConversionService.cancelConversion(taskId);
            
            if (!cancelled) {
                // If not running, just update the database
                taskService.cancelTask(taskId);
            }
            
            return taskService.getTaskResponse(taskId);
            
        } catch (Exception e) {
            logger.error("Failed to cancel task {}: {}", taskId, e.getMessage(), e);
            throw new ConversionException("Failed to cancel task: " + e.getMessage(), e);
        }
    }

    /**
     * Delete a conversion task and its files
     */
    public void deleteTask(String taskId) {
        logger.info("Deleting conversion task: {}", taskId);
        
        try {
            // Cancel if running
            if (asyncConversionService.isTaskRunning(taskId)) {
                asyncConversionService.cancelConversion(taskId);
            }
            
            // Delete files
            fileStorageService.deleteTaskFiles(taskId);
            
            // Delete task from database
            taskService.deleteTask(taskId);
            
            logger.info("Successfully deleted task: {}", taskId);
            
        } catch (Exception e) {
            logger.error("Failed to delete task {}: {}", taskId, e.getMessage(), e);
            throw new ConversionException("Failed to delete task: " + e.getMessage(), e);
        }
    }

    /**
     * Get file download information
     */
    public FileDownloadInfo getDownloadInfo(String taskId) {
        ConversionTask task = taskService.getTask(taskId);
        
        if (task.getTargetPath() == null) {
            throw new ConversionException("Task has no result file available");
        }
        
        FileStorageService.FileInfo fileInfo = fileStorageService.getFileInfo(taskId, task.getTargetFileName());
        
        if (fileInfo == null) {
            throw new ConversionException("Result file not found");
        }
        
        return new FileDownloadInfo(
            task.getTargetFileName(),
            fileInfo.getFileSize(),
            "text/markdown",
            task.getTargetPath()
        );
    }

    /**
     * Get system statistics
     */
    public SystemStatistics getSystemStatistics() {
        ConversionTaskService.TaskStatistics taskStats = taskService.getTaskStatistics();
        int runningTasks = asyncConversionService.getRunningTaskCount();
        
        return new SystemStatistics(
            taskStats.getTotalTasks(),
            taskStats.getTasksToday(),
            taskStats.getCompletedToday(),
            runningTasks,
            taskStats.getAverageProcessingTimeMs()
        );
    }

    // Helper methods
    private ConversionTask createConversionTask(MultipartFile file, ConversionRequest request, 
                                              FileValidationService.ValidationResult validationResult) {
        
        String fileName = validationResult.getOriginalFilename();
        String fileExtension = validationResult.getFileExtension();
        long fileSize = validationResult.getFileSize();
        
        // Create a temporary task to get the ID
        ConversionTask task = new ConversionTask();
        task.setTaskId(java.util.UUID.randomUUID().toString());
        task.setFileName(fileName);
        task.setFileSize(fileSize);
        task.setFileType(fileExtension);
        
        // Set target file name
        if (request.getTargetFileName() != null && !request.getTargetFileName().trim().isEmpty()) {
            task.setTargetFileName(request.getTargetFileName());
        } else {
            task.setTargetFileName(generateTargetFileName(fileName));
        }
        
        return task;
    }

    private String generateTargetFileName(String originalFileName) {
        String nameWithoutExtension = originalFileName.replaceFirst("[.][^.]+$", "");
        return nameWithoutExtension + ".md";
    }

    private String serializeConversionOptions(ConversionRequest request) {
        try {
            Map<String, Object> options = new HashMap<>();
            
            if (request.getOptions() != null) {
                options.putAll(request.getOptions());
            }
            
            options.put("enableAi", request.getEnableAi());
            options.put("preserveStructure", request.getPreserveStructure());
            options.put("extractImages", request.getExtractImages());
            options.put("extractMetadata", request.getExtractMetadata());
            
            return objectMapper.writeValueAsString(options);
            
        } catch (Exception e) {
            logger.warn("Failed to serialize conversion options: {}", e.getMessage());
            return "{}";
        }
    }

    /**
     * File download information
     */
    public static class FileDownloadInfo {
        private final String fileName;
        private final long fileSize;
        private final String contentType;
        private final String filePath;

        public FileDownloadInfo(String fileName, long fileSize, String contentType, String filePath) {
            this.fileName = fileName;
            this.fileSize = fileSize;
            this.contentType = contentType;
            this.filePath = filePath;
        }

        public String getFileName() { return fileName; }
        public long getFileSize() { return fileSize; }
        public String getContentType() { return contentType; }
        public String getFilePath() { return filePath; }
    }

    /**
     * System statistics
     */
    public static class SystemStatistics {
        private final long totalTasks;
        private final long tasksToday;
        private final long completedToday;
        private final int runningTasks;
        private final double averageProcessingTimeMs;

        public SystemStatistics(long totalTasks, long tasksToday, long completedToday, 
                              int runningTasks, double averageProcessingTimeMs) {
            this.totalTasks = totalTasks;
            this.tasksToday = tasksToday;
            this.completedToday = completedToday;
            this.runningTasks = runningTasks;
            this.averageProcessingTimeMs = averageProcessingTimeMs;
        }

        public long getTotalTasks() { return totalTasks; }
        public long getTasksToday() { return tasksToday; }
        public long getCompletedToday() { return completedToday; }
        public int getRunningTasks() { return runningTasks; }
        public double getAverageProcessingTimeMs() { return averageProcessingTimeMs; }
    }
}
