package com.talkweb.ai.indexer.web.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

/**
 * Async configuration for document conversion tasks
 */
@Configuration
@EnableAsync
@Profile("server")
public class AsyncConfig {

    @Value("${performance.async.core-pool-size:4}")
    private int corePoolSize;

    @Value("${performance.async.max-pool-size:20}")
    private int maxPoolSize;

    @Value("${performance.async.queue-capacity:100}")
    private int queueCapacity;

    @Value("${performance.async.thread-name-prefix:doc-converter-async-}")
    private String threadNamePrefix;

    @Value("${performance.async.keep-alive-seconds:60}")
    private int keepAliveSeconds;

    @Bean(name = "taskExecutor")
    public Executor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setQueueCapacity(queueCapacity);
        executor.setThreadNamePrefix(threadNamePrefix);
        executor.setKeepAliveSeconds(keepAliveSeconds);
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        executor.initialize();
        return executor;
    }

    @Bean(name = "conversionTaskExecutor")
    public Executor conversionTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(Math.max(2, corePoolSize / 2));
        executor.setMaxPoolSize(Math.max(4, maxPoolSize / 2));
        executor.setQueueCapacity(queueCapacity);
        executor.setThreadNamePrefix("conversion-task-");
        executor.setKeepAliveSeconds(keepAliveSeconds);
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(120);
        executor.initialize();
        return executor;
    }
}
