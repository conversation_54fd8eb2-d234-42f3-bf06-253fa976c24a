package com.talkweb.ai.indexer.web;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.context.annotation.Profile;

/**
 * Web server application for Document Converter
 * This application only includes web-related components
 */
@SpringBootApplication
@Profile("server")
@ComponentScan(
    basePackages = {
        "com.talkweb.ai.indexer.web"
    },
    excludeFilters = {
        @ComponentScan.Filter(
            type = FilterType.REGEX,
            pattern = "com\\.talkweb\\.ai\\.indexer\\.core\\.impl\\..*"
        ),
        @ComponentScan.Filter(
            type = FilterType.REGEX,
            pattern = "com\\.talkweb\\.ai\\.indexer\\.service\\..*"
        ),
        @ComponentScan.Filter(
            type = FilterType.REGEX,
            pattern = "com\\.talkweb\\.ai\\.indexer\\.util\\.ai\\..*"
        )
    }
)
public class WebServerApplication {

    public static void main(String[] args) {
        SpringApplication.run(WebServerApplication.class, args);
    }
}
