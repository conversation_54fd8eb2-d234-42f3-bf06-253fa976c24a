package com.talkweb.ai.indexer.web.repository;

import com.talkweb.ai.indexer.web.model.ConversionTask;
import com.talkweb.ai.indexer.web.model.TaskStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for ConversionTask entity
 */
@Repository
public interface ConversionTaskRepository extends JpaRepository<ConversionTask, String> {

    /**
     * Find tasks by status
     */
    List<ConversionTask> findByStatus(TaskStatus status);

    /**
     * Find tasks by status with pagination
     */
    Page<ConversionTask> findByStatus(TaskStatus status, Pageable pageable);

    /**
     * Find tasks by multiple statuses
     */
    List<ConversionTask> findByStatusIn(List<TaskStatus> statuses);

    /**
     * Find tasks by multiple statuses with pagination
     */
    Page<ConversionTask> findByStatusIn(List<TaskStatus> statuses, Pageable pageable);

    /**
     * Find tasks created after a specific date
     */
    List<ConversionTask> findByCreatedAtAfter(LocalDateTime dateTime);

    /**
     * Find tasks created between two dates
     */
    List<ConversionTask> findByCreatedAtBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Find tasks by file name pattern
     */
    List<ConversionTask> findByFileNameContainingIgnoreCase(String fileName);

    /**
     * Find tasks by file type
     */
    List<ConversionTask> findByFileType(String fileType);

    /**
     * Find tasks by file type with pagination
     */
    Page<ConversionTask> findByFileType(String fileType, Pageable pageable);

    /**
     * Find all tasks ordered by creation date descending
     */
    List<ConversionTask> findAllByOrderByCreatedAtDesc();

    /**
     * Find all tasks with pagination ordered by creation date descending
     */
    Page<ConversionTask> findAllByOrderByCreatedAtDesc(Pageable pageable);

    /**
     * Find tasks that are currently processing
     */
    @Query("SELECT t FROM ConversionTask t WHERE t.status = 'PROCESSING'")
    List<ConversionTask> findProcessingTasks();

    /**
     * Find tasks that can be cancelled (PENDING or PROCESSING)
     */
    @Query("SELECT t FROM ConversionTask t WHERE t.status IN ('PENDING', 'PROCESSING')")
    List<ConversionTask> findCancellableTasks();

    /**
     * Find tasks that are older than specified minutes and still processing
     */
    @Query("SELECT t FROM ConversionTask t WHERE t.status = 'PROCESSING' AND t.startedAt < :cutoffTime")
    List<ConversionTask> findStuckTasks(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * Find completed tasks older than specified date for cleanup
     */
    @Query("SELECT t FROM ConversionTask t WHERE t.status IN ('COMPLETED', 'FAILED', 'CANCELLED') AND t.completedAt < :cutoffTime")
    List<ConversionTask> findTasksForCleanup(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * Count tasks by status
     */
    long countByStatus(TaskStatus status);

    /**
     * Count tasks created today
     */
    @Query("SELECT COUNT(t) FROM ConversionTask t WHERE DATE(t.createdAt) = CURRENT_DATE")
    long countTasksCreatedToday();

    /**
     * Count tasks completed today
     */
    @Query("SELECT COUNT(t) FROM ConversionTask t WHERE t.status = 'COMPLETED' AND DATE(t.completedAt) = CURRENT_DATE")
    long countTasksCompletedToday();

    /**
     * Get average processing time for completed tasks
     */
    @Query("SELECT AVG(t.processingTimeMs) FROM ConversionTask t WHERE t.status = 'COMPLETED' AND t.processingTimeMs IS NOT NULL")
    Optional<Double> getAverageProcessingTime();

    /**
     * Get processing statistics
     */
    @Query("SELECT t.status, COUNT(t) FROM ConversionTask t GROUP BY t.status")
    List<Object[]> getTaskStatusStatistics();

    /**
     * Update task status
     */
    @Modifying
    @Query("UPDATE ConversionTask t SET t.status = :status, t.updatedAt = CURRENT_TIMESTAMP WHERE t.taskId = :taskId")
    int updateTaskStatus(@Param("taskId") String taskId, @Param("status") TaskStatus status);

    /**
     * Update task progress
     */
    @Modifying
    @Query("UPDATE ConversionTask t SET t.progress = :progress, t.updatedAt = CURRENT_TIMESTAMP WHERE t.taskId = :taskId")
    int updateTaskProgress(@Param("taskId") String taskId, @Param("progress") Integer progress);

    /**
     * Delete tasks older than specified date
     */
    @Modifying
    @Query("DELETE FROM ConversionTask t WHERE t.completedAt < :cutoffTime AND t.status IN ('COMPLETED', 'FAILED', 'CANCELLED')")
    int deleteOldTasks(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * Find tasks with custom query for complex filtering
     */
    @Query("SELECT t FROM ConversionTask t WHERE " +
           "(:status IS NULL OR t.status = :status) AND " +
           "(:fileType IS NULL OR t.fileType = :fileType) AND " +
           "(:startDate IS NULL OR t.createdAt >= :startDate) AND " +
           "(:endDate IS NULL OR t.createdAt <= :endDate) AND " +
           "(:fileName IS NULL OR LOWER(t.fileName) LIKE LOWER(CONCAT('%', :fileName, '%')))")
    Page<ConversionTask> findTasksWithFilters(
            @Param("status") TaskStatus status,
            @Param("fileType") String fileType,
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate,
            @Param("fileName") String fileName,
            Pageable pageable);

    /**
     * Check if a task exists by ID
     */
    boolean existsByTaskId(String taskId);

    /**
     * Find the most recent task for a specific file
     */
    @Query("SELECT t FROM ConversionTask t WHERE t.fileName = :fileName ORDER BY t.createdAt DESC")
    List<ConversionTask> findMostRecentTaskForFile(@Param("fileName") String fileName, Pageable pageable);
}
