package com.talkweb.ai.indexer.web.service;

import com.talkweb.ai.indexer.web.exception.ConversionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.PostConstruct;
import java.io.IOException;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.nio.file.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.UUID;

/**
 * Service for managing file storage operations
 */
@Service
@Profile("server")
public class FileStorageService {

    private static final Logger logger = LoggerFactory.getLogger(FileStorageService.class);

    // Allowed file extensions
    private static final Set<String> ALLOWED_EXTENSIONS = Set.of(
        "pdf", "docx", "doc", "xlsx", "xls", "pptx", "ppt", 
        "html", "htm", "txt", "rtf", "odt",
        "png", "jpg", "jpeg", "tiff", "bmp", "gif"
    );

    // Blocked file extensions for security
    private static final Set<String> BLOCKED_EXTENSIONS = Set.of(
        "exe", "bat", "sh", "cmd", "scr", "com", "pif", "vbs", "js"
    );

    // MIME type validation
    private static final Set<String> ALLOWED_MIME_TYPES = Set.of(
        "application/pdf",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "application/vnd.ms-excel",
        "application/vnd.openxmlformats-officedocument.presentationml.presentation",
        "application/vnd.ms-powerpoint",
        "text/html",
        "text/plain",
        "application/rtf",
        "application/vnd.oasis.opendocument.text",
        "image/png",
        "image/jpeg",
        "image/tiff",
        "image/bmp",
        "image/gif"
    );

    @Value("${doc-converter.web.upload-dir:temp/uploads}")
    private String uploadDir;

    @Value("${doc-converter.web.result-dir:temp/results}")
    private String resultDir;

    @Value("${doc-converter.web.security.max-upload-size:104857600}")
    private long maxUploadSize;

    private Path uploadPath;
    private Path resultPath;

    @PostConstruct
    public void init() {
        try {
            uploadPath = Paths.get(uploadDir).toAbsolutePath().normalize();
            resultPath = Paths.get(resultDir).toAbsolutePath().normalize();
            
            Files.createDirectories(uploadPath);
            Files.createDirectories(resultPath);
            
            logger.info("File storage initialized - Upload: {}, Result: {}", uploadPath, resultPath);
        } catch (IOException e) {
            throw new ConversionException("Failed to initialize file storage directories", e);
        }
    }

    /**
     * Store uploaded file
     */
    public StoredFile storeFile(MultipartFile file, String taskId) {
        validateFile(file);
        
        String originalFileName = StringUtils.cleanPath(file.getOriginalFilename());
        String fileExtension = getFileExtension(originalFileName);
        String storedFileName = generateStoredFileName(taskId, originalFileName);
        
        try {
            // Create task-specific directory
            Path taskDir = uploadPath.resolve(taskId);
            Files.createDirectories(taskDir);
            
            // Store the file
            Path targetPath = taskDir.resolve(storedFileName);
            
            // Prevent directory traversal
            if (!targetPath.normalize().startsWith(taskDir.normalize())) {
                throw new ConversionException("Invalid file path: " + storedFileName);
            }
            
            try (InputStream inputStream = file.getInputStream()) {
                Files.copy(inputStream, targetPath, StandardCopyOption.REPLACE_EXISTING);
            }
            
            logger.info("Stored file {} for task {}", storedFileName, taskId);
            
            return new StoredFile(
                taskId,
                originalFileName,
                storedFileName,
                targetPath.toString(),
                file.getSize(),
                fileExtension,
                file.getContentType()
            );
            
        } catch (IOException e) {
            throw new ConversionException("Failed to store file: " + originalFileName, e);
        }
    }

    /**
     * Store result file
     */
    public StoredFile storeResultFile(String taskId, String content, String fileName) {
        try {
            // Create task-specific result directory
            Path taskResultDir = resultPath.resolve(taskId);
            Files.createDirectories(taskResultDir);
            
            // Create temporary file first
            String tempFileName = fileName + ".tmp";
            Path tempPath = taskResultDir.resolve(tempFileName);
            
            // Write content to temporary file
            Files.write(tempPath, content.getBytes(), StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
            
            // Rename to final file
            Path finalPath = taskResultDir.resolve(fileName);
            Files.move(tempPath, finalPath, StandardCopyOption.REPLACE_EXISTING);
            
            long fileSize = Files.size(finalPath);
            
            logger.info("Stored result file {} for task {}", fileName, taskId);
            
            return new StoredFile(
                taskId,
                fileName,
                fileName,
                finalPath.toString(),
                fileSize,
                "md",
                "text/markdown"
            );
            
        } catch (IOException e) {
            throw new ConversionException("Failed to store result file: " + fileName, e);
        }
    }

    /**
     * Load file as resource
     */
    public Resource loadFileAsResource(String taskId, String fileName) {
        try {
            Path filePath = resultPath.resolve(taskId).resolve(fileName).normalize();
            
            // Security check
            if (!filePath.startsWith(resultPath.normalize())) {
                throw new ConversionException("Invalid file path: " + fileName);
            }
            
            Resource resource = new UrlResource(filePath.toUri());
            
            if (resource.exists() && resource.isReadable()) {
                return resource;
            } else {
                throw new ConversionException("File not found or not readable: " + fileName);
            }
            
        } catch (MalformedURLException e) {
            throw new ConversionException("Invalid file path: " + fileName, e);
        }
    }

    /**
     * Delete task files
     */
    public void deleteTaskFiles(String taskId) {
        try {
            // Delete upload files
            Path taskUploadDir = uploadPath.resolve(taskId);
            if (Files.exists(taskUploadDir)) {
                deleteDirectoryRecursively(taskUploadDir);
                logger.info("Deleted upload files for task {}", taskId);
            }
            
            // Delete result files
            Path taskResultDir = resultPath.resolve(taskId);
            if (Files.exists(taskResultDir)) {
                deleteDirectoryRecursively(taskResultDir);
                logger.info("Deleted result files for task {}", taskId);
            }
            
        } catch (IOException e) {
            logger.error("Failed to delete files for task {}: {}", taskId, e.getMessage());
        }
    }

    /**
     * Clean up old files
     */
    public int cleanupOldFiles(int daysOld) {
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(daysOld);
        int deletedCount = 0;
        
        try {
            // Clean upload directory
            deletedCount += cleanupDirectory(uploadPath, cutoffTime);
            
            // Clean result directory
            deletedCount += cleanupDirectory(resultPath, cutoffTime);
            
            logger.info("Cleaned up {} old file directories", deletedCount);
            
        } catch (IOException e) {
            logger.error("Failed to cleanup old files: {}", e.getMessage());
        }
        
        return deletedCount;
    }

    /**
     * Get file info
     */
    public FileInfo getFileInfo(String taskId, String fileName) {
        Path filePath = resultPath.resolve(taskId).resolve(fileName);
        
        if (!Files.exists(filePath)) {
            return null;
        }
        
        try {
            long size = Files.size(filePath);
            LocalDateTime lastModified = LocalDateTime.ofInstant(
                Files.getLastModifiedTime(filePath).toInstant(),
                java.time.ZoneId.systemDefault()
            );
            
            return new FileInfo(fileName, size, lastModified);
            
        } catch (IOException e) {
            logger.error("Failed to get file info for {}: {}", fileName, e.getMessage());
            return null;
        }
    }

    // Validation methods
    private void validateFile(MultipartFile file) {
        if (file.isEmpty()) {
            throw new ConversionException("File is empty");
        }
        
        if (file.getSize() > maxUploadSize) {
            throw new ConversionException(
                String.format("File size %d exceeds maximum allowed size %d", file.getSize(), maxUploadSize));
        }
        
        String originalFileName = file.getOriginalFilename();
        if (originalFileName == null || originalFileName.trim().isEmpty()) {
            throw new ConversionException("File name is required");
        }
        
        String extension = getFileExtension(originalFileName).toLowerCase();
        
        if (BLOCKED_EXTENSIONS.contains(extension)) {
            throw new ConversionException("File type not allowed: " + extension);
        }
        
        if (!ALLOWED_EXTENSIONS.contains(extension)) {
            throw new ConversionException("Unsupported file type: " + extension);
        }
        
        String contentType = file.getContentType();
        if (contentType != null && !ALLOWED_MIME_TYPES.contains(contentType)) {
            logger.warn("Unexpected MIME type {} for file {}", contentType, originalFileName);
        }
    }

    // Helper methods
    private String getFileExtension(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        return lastDotIndex > 0 ? fileName.substring(lastDotIndex + 1) : "";
    }

    private String generateStoredFileName(String taskId, String originalFileName) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String extension = getFileExtension(originalFileName);
        String baseName = originalFileName.substring(0, originalFileName.lastIndexOf('.'));
        
        // Sanitize base name
        baseName = baseName.replaceAll("[^a-zA-Z0-9._-]", "_");
        
        return String.format("%s_%s_%s.%s", taskId.substring(0, 8), timestamp, baseName, extension);
    }

    private void deleteDirectoryRecursively(Path directory) throws IOException {
        Files.walk(directory)
                .sorted((a, b) -> b.compareTo(a)) // Delete files before directories
                .forEach(path -> {
                    try {
                        Files.delete(path);
                    } catch (IOException e) {
                        logger.warn("Failed to delete {}: {}", path, e.getMessage());
                    }
                });
    }

    private int cleanupDirectory(Path directory, LocalDateTime cutoffTime) throws IOException {
        if (!Files.exists(directory)) {
            return 0;
        }
        
        int deletedCount = 0;
        
        try (var stream = Files.list(directory)) {
            for (Path taskDir : stream.toList()) {
                if (Files.isDirectory(taskDir)) {
                    try {
                        LocalDateTime lastModified = LocalDateTime.ofInstant(
                            Files.getLastModifiedTime(taskDir).toInstant(),
                            java.time.ZoneId.systemDefault()
                        );
                        
                        if (lastModified.isBefore(cutoffTime)) {
                            deleteDirectoryRecursively(taskDir);
                            deletedCount++;
                        }
                    } catch (IOException e) {
                        logger.warn("Failed to process directory {}: {}", taskDir, e.getMessage());
                    }
                }
            }
        }
        
        return deletedCount;
    }

    // Inner classes
    public static class StoredFile {
        private final String taskId;
        private final String originalFileName;
        private final String storedFileName;
        private final String filePath;
        private final long fileSize;
        private final String fileExtension;
        private final String contentType;

        public StoredFile(String taskId, String originalFileName, String storedFileName, 
                         String filePath, long fileSize, String fileExtension, String contentType) {
            this.taskId = taskId;
            this.originalFileName = originalFileName;
            this.storedFileName = storedFileName;
            this.filePath = filePath;
            this.fileSize = fileSize;
            this.fileExtension = fileExtension;
            this.contentType = contentType;
        }

        // Getters
        public String getTaskId() { return taskId; }
        public String getOriginalFileName() { return originalFileName; }
        public String getStoredFileName() { return storedFileName; }
        public String getFilePath() { return filePath; }
        public long getFileSize() { return fileSize; }
        public String getFileExtension() { return fileExtension; }
        public String getContentType() { return contentType; }
    }

    public static class FileInfo {
        private final String fileName;
        private final long fileSize;
        private final LocalDateTime lastModified;

        public FileInfo(String fileName, long fileSize, LocalDateTime lastModified) {
            this.fileName = fileName;
            this.fileSize = fileSize;
            this.lastModified = lastModified;
        }

        // Getters
        public String getFileName() { return fileName; }
        public long getFileSize() { return fileSize; }
        public LocalDateTime getLastModified() { return lastModified; }
    }
}
