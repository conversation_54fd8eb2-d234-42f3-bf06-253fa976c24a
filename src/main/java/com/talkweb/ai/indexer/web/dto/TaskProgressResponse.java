package com.talkweb.ai.indexer.web.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.talkweb.ai.indexer.web.model.TaskStatus;

import java.time.LocalDateTime;

/**
 * DTO for task progress response
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TaskProgressResponse {

    private String taskId;
    private TaskStatus status;
    private Integer progress;
    private String message;
    private String currentStep;
    
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS")
    private LocalDateTime lastUpdated;
    
    private Long estimatedTimeRemainingMs;
    private String errorMessage;

    // Default constructor
    public TaskProgressResponse() {}

    // Constructor with basic fields
    public TaskProgressResponse(String taskId, TaskStatus status, Integer progress) {
        this.taskId = taskId;
        this.status = status;
        this.progress = progress;
        this.lastUpdated = LocalDateTime.now();
    }

    // Constructor with message
    public TaskProgressResponse(String taskId, TaskStatus status, Integer progress, String message) {
        this(taskId, status, progress);
        this.message = message;
    }

    // Static factory methods
    public static TaskProgressResponse pending(String taskId) {
        return new TaskProgressResponse(taskId, TaskStatus.PENDING, 0, "Task is waiting to be processed");
    }

    public static TaskProgressResponse processing(String taskId, Integer progress, String currentStep) {
        TaskProgressResponse response = new TaskProgressResponse(taskId, TaskStatus.PROCESSING, progress);
        response.setCurrentStep(currentStep);
        return response;
    }

    public static TaskProgressResponse completed(String taskId) {
        return new TaskProgressResponse(taskId, TaskStatus.COMPLETED, 100, "Task completed successfully");
    }

    public static TaskProgressResponse failed(String taskId, String errorMessage) {
        TaskProgressResponse response = new TaskProgressResponse(taskId, TaskStatus.FAILED, null, "Task failed");
        response.setErrorMessage(errorMessage);
        return response;
    }

    public static TaskProgressResponse cancelled(String taskId) {
        return new TaskProgressResponse(taskId, TaskStatus.CANCELLED, null, "Task was cancelled");
    }

    // Getters and setters
    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public TaskStatus getStatus() {
        return status;
    }

    public void setStatus(TaskStatus status) {
        this.status = status;
    }

    public Integer getProgress() {
        return progress;
    }

    public void setProgress(Integer progress) {
        this.progress = progress;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getCurrentStep() {
        return currentStep;
    }

    public void setCurrentStep(String currentStep) {
        this.currentStep = currentStep;
    }

    public LocalDateTime getLastUpdated() {
        return lastUpdated;
    }

    public void setLastUpdated(LocalDateTime lastUpdated) {
        this.lastUpdated = lastUpdated;
    }

    public Long getEstimatedTimeRemainingMs() {
        return estimatedTimeRemainingMs;
    }

    public void setEstimatedTimeRemainingMs(Long estimatedTimeRemainingMs) {
        this.estimatedTimeRemainingMs = estimatedTimeRemainingMs;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    @Override
    public String toString() {
        return "TaskProgressResponse{" +
                "taskId='" + taskId + '\'' +
                ", status=" + status +
                ", progress=" + progress +
                ", message='" + message + '\'' +
                ", currentStep='" + currentStep + '\'' +
                ", lastUpdated=" + lastUpdated +
                '}';
    }
}
