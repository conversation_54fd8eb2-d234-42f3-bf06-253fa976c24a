package com.talkweb.ai.indexer.web.interceptor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.talkweb.ai.indexer.web.config.RateLimitConfig;
import com.talkweb.ai.indexer.web.dto.ErrorResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import java.time.LocalDateTime;

/**
 * Interceptor for API rate limiting
 */
@Component
@Profile("server")
public class RateLimitInterceptor implements HandlerInterceptor {

    private static final Logger logger = LoggerFactory.getLogger(RateLimitInterceptor.class);

    private final RateLimitConfig.RateLimitService rateLimitService;
    private final ObjectMapper objectMapper;

    @Autowired
    public RateLimitInterceptor(RateLimitConfig.RateLimitService rateLimitService, ObjectMapper objectMapper) {
        this.rateLimitService = rateLimitService;
        this.objectMapper = objectMapper;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // Skip rate limiting for non-API requests
        String requestURI = request.getRequestURI();
        if (!requestURI.startsWith("/api/")) {
            return true;
        }

        // Get client IP
        String clientIp = getClientIp(request);
        
        // Check rate limit
        if (!rateLimitService.isAllowed(clientIp)) {
            logger.warn("Rate limit exceeded for client IP: {}", clientIp);
            
            // Set rate limit headers
            setRateLimitHeaders(response, clientIp);
            
            // Return 429 Too Many Requests
            response.setStatus(HttpStatus.TOO_MANY_REQUESTS.value());
            response.setContentType(MediaType.APPLICATION_JSON_VALUE);
            
            ErrorResponse errorResponse = ErrorResponse.builder()
                    .timestamp(LocalDateTime.now())
                    .status(HttpStatus.TOO_MANY_REQUESTS.value())
                    .error("Too Many Requests")
                    .message("Rate limit exceeded. Please try again later.")
                    .path(requestURI)
                    .build();
            
            response.getWriter().write(objectMapper.writeValueAsString(errorResponse));
            return false;
        }

        // Set rate limit headers for successful requests
        setRateLimitHeaders(response, clientIp);
        
        return true;
    }

    /**
     * Get client IP address from request
     */
    private String getClientIp(HttpServletRequest request) {
        // Check for X-Forwarded-For header (proxy/load balancer)
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            // Take the first IP in the chain
            return xForwardedFor.split(",")[0].trim();
        }
        
        // Check for X-Real-IP header (nginx)
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        // Check for CF-Connecting-IP header (Cloudflare)
        String cfConnectingIp = request.getHeader("CF-Connecting-IP");
        if (cfConnectingIp != null && !cfConnectingIp.isEmpty()) {
            return cfConnectingIp;
        }
        
        // Fall back to remote address
        return request.getRemoteAddr();
    }

    /**
     * Set rate limit headers in response
     */
    private void setRateLimitHeaders(HttpServletResponse response, String clientIp) {
        RateLimitConfig.RateLimitInfo rateLimitInfo = rateLimitService.getRateLimitInfo(clientIp);
        
        response.setHeader("X-RateLimit-Limit-Minute", String.valueOf(rateLimitInfo.getLimitPerMinute()));
        response.setHeader("X-RateLimit-Limit-Hour", String.valueOf(rateLimitInfo.getLimitPerHour()));
        response.setHeader("X-RateLimit-Remaining-Minute", String.valueOf(rateLimitInfo.getRemainingMinute()));
        response.setHeader("X-RateLimit-Remaining-Hour", String.valueOf(rateLimitInfo.getRemainingHour()));
    }
}
