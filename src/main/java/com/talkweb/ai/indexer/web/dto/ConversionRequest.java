package com.talkweb.ai.indexer.web.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.talkweb.ai.indexer.web.validation.ValidFileType;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * DTO for conversion task creation request
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ConversionRequest {

    @NotNull(message = "File is required")
    @ValidFileType
    private MultipartFile file;

    @Size(max = 255, message = "Target file name must not exceed 255 characters")
    private String targetFileName;

    private Map<String, Object> options;

    private Boolean enableAi = false;

    private Boolean preserveStructure = true;

    private Boolean extractImages = true;

    private Boolean extractMetadata = true;

    // Default constructor
    public ConversionRequest() {}

    // Constructor with file
    public ConversionRequest(MultipartFile file) {
        this.file = file;
    }

    // Getters and setters
    public MultipartFile getFile() {
        return file;
    }

    public void setFile(MultipartFile file) {
        this.file = file;
    }

    public String getTargetFileName() {
        return targetFileName;
    }

    public void setTargetFileName(String targetFileName) {
        this.targetFileName = targetFileName;
    }

    public Map<String, Object> getOptions() {
        return options;
    }

    public void setOptions(Map<String, Object> options) {
        this.options = options;
    }

    public Boolean getEnableAi() {
        return enableAi;
    }

    public void setEnableAi(Boolean enableAi) {
        this.enableAi = enableAi;
    }

    public Boolean getPreserveStructure() {
        return preserveStructure;
    }

    public void setPreserveStructure(Boolean preserveStructure) {
        this.preserveStructure = preserveStructure;
    }

    public Boolean getExtractImages() {
        return extractImages;
    }

    public void setExtractImages(Boolean extractImages) {
        this.extractImages = extractImages;
    }

    public Boolean getExtractMetadata() {
        return extractMetadata;
    }

    public void setExtractMetadata(Boolean extractMetadata) {
        this.extractMetadata = extractMetadata;
    }

    @Override
    public String toString() {
        return "ConversionRequest{" +
                "file=" + (file != null ? file.getOriginalFilename() : "null") +
                ", targetFileName='" + targetFileName + '\'' +
                ", enableAi=" + enableAi +
                ", preserveStructure=" + preserveStructure +
                ", extractImages=" + extractImages +
                ", extractMetadata=" + extractMetadata +
                '}';
    }
}
