package com.talkweb.ai.indexer.web.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * Service for handling scheduled tasks related to conversion management
 */
@Service
@Profile("server")
@EnableScheduling
public class TaskSchedulingService {

    private static final Logger logger = LoggerFactory.getLogger(TaskSchedulingService.class);

    private final ConversionTaskService taskService;
    private final FileStorageService fileStorageService;
    private final TaskProgressService progressService;

    @Value("${doc-converter.web.task-timeout:300}")
    private int taskTimeoutSeconds;

    @Value("${doc-converter.web.max-file-age:86400}")
    private int maxFileAgeSeconds;

    @Value("${doc-converter.web.max-task-history:1000}")
    private int maxTaskHistory;

    @Autowired
    public TaskSchedulingService(
            ConversionTaskService taskService,
            FileStorageService fileStorageService,
            TaskProgressService progressService) {
        this.taskService = taskService;
        this.fileStorageService = fileStorageService;
        this.progressService = progressService;
    }

    /**
     * Check for stuck tasks every 5 minutes
     */
    @Scheduled(fixedRate = 300000) // 5 minutes
    public void checkStuckTasks() {
        try {
            logger.debug("Checking for stuck tasks...");
            
            int timeoutMinutes = taskTimeoutSeconds / 60;
            progressService.checkForStuckTasks(timeoutMinutes);
            
            // Also check database for stuck tasks
            var stuckTasks = taskService.getStuckTasks(timeoutMinutes);
            
            for (var task : stuckTasks) {
                logger.warn("Found stuck task in database: {}", task.getTaskId());
                taskService.markTaskFailed(task.getTaskId(), "Task timeout - exceeded maximum processing time");
            }
            
            if (!stuckTasks.isEmpty()) {
                logger.info("Marked {} stuck tasks as failed", stuckTasks.size());
            }
            
        } catch (Exception e) {
            logger.error("Error checking for stuck tasks: {}", e.getMessage(), e);
        }
    }

    /**
     * Clean up old files every hour
     */
    @Scheduled(fixedRate = 3600000) // 1 hour
    public void cleanupOldFiles() {
        try {
            logger.debug("Cleaning up old files...");
            
            int daysOld = maxFileAgeSeconds / 86400; // Convert seconds to days
            int deletedFiles = fileStorageService.cleanupOldFiles(daysOld);
            
            if (deletedFiles > 0) {
                logger.info("Cleaned up {} old file directories", deletedFiles);
            }
            
        } catch (Exception e) {
            logger.error("Error cleaning up old files: {}", e.getMessage(), e);
        }
    }

    /**
     * Clean up old completed tasks every 6 hours
     */
    @Scheduled(fixedRate = 21600000) // 6 hours
    public void cleanupOldTasks() {
        try {
            logger.debug("Cleaning up old completed tasks...");
            
            // Keep tasks for 7 days by default
            int daysOld = 7;
            int deletedTasks = taskService.cleanupOldTasks(daysOld);
            
            if (deletedTasks > 0) {
                logger.info("Cleaned up {} old completed tasks", deletedTasks);
            }
            
        } catch (Exception e) {
            logger.error("Error cleaning up old tasks: {}", e.getMessage(), e);
        }
    }

    /**
     * Log system statistics every 30 minutes
     */
    @Scheduled(fixedRate = 1800000) // 30 minutes
    public void logSystemStatistics() {
        try {
            var stats = taskService.getTaskStatistics();
            
            logger.info("System Statistics - Total tasks: {}, Tasks today: {}, Completed today: {}, Avg processing time: {:.2f}s",
                stats.getTotalTasks(),
                stats.getTasksToday(),
                stats.getCompletedToday(),
                stats.getAverageProcessingTimeMs() / 1000.0
            );
            
            // Log status breakdown
            for (Object[] statusCount : stats.getStatusCounts()) {
                logger.info("Tasks with status {}: {}", statusCount[0], statusCount[1]);
            }
            
        } catch (Exception e) {
            logger.error("Error logging system statistics: {}", e.getMessage(), e);
        }
    }

    /**
     * Health check every minute
     */
    @Scheduled(fixedRate = 60000) // 1 minute
    public void performHealthCheck() {
        try {
            // Check if the system is healthy
            long processingTasks = taskService.getProcessingTasks().size();
            
            if (processingTasks > 0) {
                logger.debug("Health check: {} tasks currently processing", processingTasks);
            }
            
            // Check memory usage
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            double memoryUsagePercent = (double) usedMemory / totalMemory * 100;
            
            if (memoryUsagePercent > 80) {
                logger.warn("High memory usage detected: {:.1f}%", memoryUsagePercent);
            }
            
        } catch (Exception e) {
            logger.error("Error during health check: {}", e.getMessage(), e);
        }
    }

    /**
     * Clean up progress data for terminal tasks every 10 minutes
     */
    @Scheduled(fixedRate = 600000) // 10 minutes
    public void cleanupProgressData() {
        try {
            logger.debug("Cleaning up progress data...");
            
            // This would be implemented if we had a way to get all task IDs
            // For now, the progress service handles its own cleanup
            
        } catch (Exception e) {
            logger.error("Error cleaning up progress data: {}", e.getMessage(), e);
        }
    }
}
