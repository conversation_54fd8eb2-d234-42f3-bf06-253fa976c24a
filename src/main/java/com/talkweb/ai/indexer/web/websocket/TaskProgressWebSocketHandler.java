package com.talkweb.ai.indexer.web.websocket;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.talkweb.ai.indexer.web.dto.TaskProgressResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;

import java.io.IOException;
import java.net.URI;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * WebSocket handler for real-time task progress updates
 */
@Component
@Profile("server")
public class TaskProgressWebSocketHandler implements WebSocketHandler {

    private static final Logger logger = LoggerFactory.getLogger(TaskProgressWebSocketHandler.class);

    private final ObjectMapper objectMapper;

    // Store active WebSocket sessions
    private final ConcurrentHashMap<String, CopyOnWriteArraySet<WebSocketSession>> taskSubscriptions = new ConcurrentHashMap<>();
    private final CopyOnWriteArraySet<WebSocketSession> systemSubscriptions = new CopyOnWriteArraySet<>();
    private final ConcurrentHashMap<String, WebSocketSession> allSessions = new ConcurrentHashMap<>();

    // Pattern to extract task ID from WebSocket path
    private static final Pattern TASK_ID_PATTERN = Pattern.compile("/ws/tasks/([^/]+)");

    @Autowired
    public TaskProgressWebSocketHandler(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        String sessionId = session.getId();
        String path = session.getUri().getPath();
        
        logger.info("WebSocket connection established: {} for path: {}", sessionId, path);
        
        // Store session
        allSessions.put(sessionId, session);
        
        // Subscribe to appropriate channel based on path
        if (path.equals("/ws/system")) {
            systemSubscriptions.add(session);
            logger.debug("Session {} subscribed to system notifications", sessionId);
        } else if (path.equals("/ws/tasks")) {
            // Subscribe to all task updates
            subscribeToAllTasks(session);
            logger.debug("Session {} subscribed to all task updates", sessionId);
        } else {
            // Extract task ID and subscribe to specific task
            Matcher matcher = TASK_ID_PATTERN.matcher(path);
            if (matcher.find()) {
                String taskId = matcher.group(1);
                subscribeToTask(session, taskId);
                logger.debug("Session {} subscribed to task {}", sessionId, taskId);
            }
        }
        
        // Send welcome message
        sendWelcomeMessage(session);
    }

    @Override
    public void handleMessage(WebSocketSession session, org.springframework.web.socket.WebSocketMessage<?> message) throws Exception {
        String sessionId = session.getId();
        String payload = message.getPayload().toString();
        
        logger.debug("Received message from session {}: {}", sessionId, payload);
        
        try {
            // Parse incoming message
            WebSocketMessage incomingMessage = objectMapper.readValue(payload, WebSocketMessage.class);
            
            // Handle different message types
            switch (incomingMessage.getType()) {
                case "SUBSCRIBE":
                    handleSubscribeMessage(session, incomingMessage);
                    break;
                case "UNSUBSCRIBE":
                    handleUnsubscribeMessage(session, incomingMessage);
                    break;
                case "PING":
                    handlePingMessage(session);
                    break;
                default:
                    logger.warn("Unknown message type: {}", incomingMessage.getType());
                    break;
            }
            
        } catch (Exception e) {
            logger.error("Error handling WebSocket message from session {}: {}", sessionId, e.getMessage());
            sendErrorMessage(session, "Invalid message format");
        }
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        String sessionId = session.getId();
        logger.error("WebSocket transport error for session {}: {}", sessionId, exception.getMessage());
        
        // Clean up session
        cleanupSession(session);
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        String sessionId = session.getId();
        logger.info("WebSocket connection closed: {} with status: {}", sessionId, closeStatus);
        
        // Clean up session
        cleanupSession(session);
    }

    @Override
    public boolean supportsPartialMessages() {
        return false;
    }

    /**
     * Send task progress update to subscribed clients
     */
    public void sendTaskProgressUpdate(String taskId, TaskProgressResponse progress) {
        WebSocketMessage message = new WebSocketMessage("TASK_PROGRESS_UPDATE", progress);
        
        try {
            String jsonMessage = objectMapper.writeValueAsString(message);
            
            // Send to task-specific subscribers
            CopyOnWriteArraySet<WebSocketSession> taskSessions = taskSubscriptions.get(taskId);
            if (taskSessions != null) {
                sendToSessions(taskSessions, jsonMessage);
            }
            
            // Send to all-tasks subscribers
            CopyOnWriteArraySet<WebSocketSession> allTasksSessions = taskSubscriptions.get("*");
            if (allTasksSessions != null) {
                sendToSessions(allTasksSessions, jsonMessage);
            }
            
        } catch (Exception e) {
            logger.error("Error sending task progress update: {}", e.getMessage());
        }
    }

    /**
     * Send system notification to all system subscribers
     */
    public void sendSystemNotification(Object notification) {
        WebSocketMessage message = new WebSocketMessage("SYSTEM_NOTIFICATION", notification);
        
        try {
            String jsonMessage = objectMapper.writeValueAsString(message);
            sendToSessions(systemSubscriptions, jsonMessage);
            
        } catch (Exception e) {
            logger.error("Error sending system notification: {}", e.getMessage());
        }
    }

    /**
     * Broadcast message to all connected clients
     */
    public void broadcastMessage(String type, Object data) {
        WebSocketMessage message = new WebSocketMessage(type, data);
        
        try {
            String jsonMessage = objectMapper.writeValueAsString(message);
            
            for (WebSocketSession session : allSessions.values()) {
                if (session.isOpen()) {
                    try {
                        session.sendMessage(new TextMessage(jsonMessage));
                    } catch (IOException e) {
                        logger.warn("Failed to send broadcast message to session {}: {}", 
                            session.getId(), e.getMessage());
                    }
                }
            }
            
        } catch (Exception e) {
            logger.error("Error broadcasting message: {}", e.getMessage());
        }
    }

    // Helper methods
    private void subscribeToTask(WebSocketSession session, String taskId) {
        taskSubscriptions.computeIfAbsent(taskId, k -> new CopyOnWriteArraySet<>()).add(session);
    }

    private void subscribeToAllTasks(WebSocketSession session) {
        taskSubscriptions.computeIfAbsent("*", k -> new CopyOnWriteArraySet<>()).add(session);
    }

    private void handleSubscribeMessage(WebSocketSession session, WebSocketMessage message) {
        String taskId = (String) message.getData();
        if (taskId != null) {
            subscribeToTask(session, taskId);
            sendAckMessage(session, "Subscribed to task: " + taskId);
        }
    }

    private void handleUnsubscribeMessage(WebSocketSession session, WebSocketMessage message) {
        String taskId = (String) message.getData();
        if (taskId != null) {
            CopyOnWriteArraySet<WebSocketSession> sessions = taskSubscriptions.get(taskId);
            if (sessions != null) {
                sessions.remove(session);
                sendAckMessage(session, "Unsubscribed from task: " + taskId);
            }
        }
    }

    private void handlePingMessage(WebSocketSession session) {
        sendPongMessage(session);
    }

    private void sendWelcomeMessage(WebSocketSession session) {
        WebSocketMessage welcome = new WebSocketMessage("WELCOME", 
            "Connected to Document Converter WebSocket");
        sendMessage(session, welcome);
    }

    private void sendAckMessage(WebSocketSession session, String message) {
        WebSocketMessage ack = new WebSocketMessage("ACK", message);
        sendMessage(session, ack);
    }

    private void sendPongMessage(WebSocketSession session) {
        WebSocketMessage pong = new WebSocketMessage("PONG", System.currentTimeMillis());
        sendMessage(session, pong);
    }

    private void sendErrorMessage(WebSocketSession session, String error) {
        WebSocketMessage errorMsg = new WebSocketMessage("ERROR", error);
        sendMessage(session, errorMsg);
    }

    private void sendMessage(WebSocketSession session, WebSocketMessage message) {
        try {
            String jsonMessage = objectMapper.writeValueAsString(message);
            session.sendMessage(new TextMessage(jsonMessage));
        } catch (Exception e) {
            logger.error("Error sending message to session {}: {}", session.getId(), e.getMessage());
        }
    }

    private void sendToSessions(CopyOnWriteArraySet<WebSocketSession> sessions, String message) {
        for (WebSocketSession session : sessions) {
            if (session.isOpen()) {
                try {
                    session.sendMessage(new TextMessage(message));
                } catch (IOException e) {
                    logger.warn("Failed to send message to session {}: {}", session.getId(), e.getMessage());
                    // Remove failed session
                    sessions.remove(session);
                }
            } else {
                // Remove closed session
                sessions.remove(session);
            }
        }
    }

    private void cleanupSession(WebSocketSession session) {
        String sessionId = session.getId();
        
        // Remove from all sessions
        allSessions.remove(sessionId);
        
        // Remove from system subscriptions
        systemSubscriptions.remove(session);
        
        // Remove from task subscriptions
        taskSubscriptions.values().forEach(sessions -> sessions.remove(session));
        
        // Clean up empty subscription sets
        taskSubscriptions.entrySet().removeIf(entry -> entry.getValue().isEmpty());
    }

    /**
     * Get connection statistics
     */
    public ConnectionStats getConnectionStats() {
        int totalConnections = allSessions.size();
        int systemSubscribers = systemSubscriptions.size();
        int taskSubscriptions = this.taskSubscriptions.values().stream()
            .mapToInt(CopyOnWriteArraySet::size)
            .sum();
        
        return new ConnectionStats(totalConnections, systemSubscribers, taskSubscriptions);
    }

    /**
     * WebSocket message structure
     */
    public static class WebSocketMessage {
        private String type;
        private Object data;
        private long timestamp;

        public WebSocketMessage() {
            this.timestamp = System.currentTimeMillis();
        }

        public WebSocketMessage(String type, Object data) {
            this();
            this.type = type;
            this.data = data;
        }

        // Getters and setters
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        public Object getData() { return data; }
        public void setData(Object data) { this.data = data; }
        public long getTimestamp() { return timestamp; }
        public void setTimestamp(long timestamp) { this.timestamp = timestamp; }
    }

    /**
     * Connection statistics
     */
    public static class ConnectionStats {
        private final int totalConnections;
        private final int systemSubscribers;
        private final int taskSubscriptions;

        public ConnectionStats(int totalConnections, int systemSubscribers, int taskSubscriptions) {
            this.totalConnections = totalConnections;
            this.systemSubscribers = systemSubscribers;
            this.taskSubscriptions = taskSubscriptions;
        }

        // Getters
        public int getTotalConnections() { return totalConnections; }
        public int getSystemSubscribers() { return systemSubscribers; }
        public int getTaskSubscriptions() { return taskSubscriptions; }
    }
}
