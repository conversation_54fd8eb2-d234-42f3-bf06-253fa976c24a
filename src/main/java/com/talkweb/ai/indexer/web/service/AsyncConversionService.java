package com.talkweb.ai.indexer.web.service;

import com.talkweb.ai.indexer.core.PluginManager;
import com.talkweb.ai.indexer.web.exception.ConversionException;
import com.talkweb.ai.indexer.web.model.ConversionTask;
import com.talkweb.ai.indexer.web.model.TaskStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;
import java.util.Map;

/**
 * Service for handling asynchronous document conversion
 */
@Service
@Profile("server")
public class AsyncConversionService {

    private static final Logger logger = LoggerFactory.getLogger(AsyncConversionService.class);

    private final ConversionTaskService taskService;
    private final FileStorageService fileStorageService;
    private final TaskProgressService progressService;
    private final PluginManager pluginManager;

    // Track running tasks for cancellation
    private final Map<String, Future<?>> runningTasks = new ConcurrentHashMap<>();

    @Autowired
    public AsyncConversionService(
            ConversionTaskService taskService,
            FileStorageService fileStorageService,
            TaskProgressService progressService,
            PluginManager pluginManager) {
        this.taskService = taskService;
        this.fileStorageService = fileStorageService;
        this.progressService = progressService;
        this.pluginManager = pluginManager;
    }

    /**
     * Start asynchronous conversion of a document
     */
    @Async("conversionTaskExecutor")
    public CompletableFuture<ConversionTask> convertDocumentAsync(String taskId) {
        logger.info("Starting async conversion for task {}", taskId);
        
        try {
            ConversionTask task = taskService.getTask(taskId);
            
            // Register the task as running
            Future<?> currentFuture = CompletableFuture.completedFuture(null);
            runningTasks.put(taskId, currentFuture);
            
            try {
                // Mark task as started
                task = taskService.updateTaskStatus(taskId, TaskStatus.PROCESSING);
                progressService.markTaskStarted(taskId);
                
                // Perform the conversion
                ConversionResult result = performConversion(task);
                
                // Store the result
                FileStorageService.StoredFile resultFile = fileStorageService.storeResultFile(
                    taskId, result.getContent(), result.getFileName()
                );
                
                // Mark task as completed
                task = taskService.markTaskCompleted(taskId, resultFile.getFilePath(), resultFile.getFileSize());
                progressService.markTaskCompleted(taskId);
                
                logger.info("Completed conversion for task {}", taskId);
                return CompletableFuture.completedFuture(task);
                
            } catch (Exception e) {
                logger.error("Conversion failed for task {}: {}", taskId, e.getMessage(), e);
                
                // Mark task as failed
                task = taskService.markTaskFailed(taskId, e.getMessage());
                progressService.markTaskFailed(taskId, e.getMessage());
                
                throw new ConversionException("Conversion failed: " + e.getMessage(), e);
            } finally {
                // Remove from running tasks
                runningTasks.remove(taskId);
            }
            
        } catch (Exception e) {
            logger.error("Failed to start conversion for task {}: {}", taskId, e.getMessage(), e);
            throw new ConversionException("Failed to start conversion", e);
        }
    }

    /**
     * Cancel a running conversion task
     */
    public boolean cancelConversion(String taskId) {
        Future<?> runningTask = runningTasks.get(taskId);
        
        if (runningTask != null && !runningTask.isDone()) {
            boolean cancelled = runningTask.cancel(true);
            
            if (cancelled) {
                runningTasks.remove(taskId);
                taskService.cancelTask(taskId);
                progressService.markTaskCancelled(taskId);
                
                logger.info("Cancelled conversion for task {}", taskId);
                return true;
            }
        }
        
        return false;
    }

    /**
     * Check if a task is currently running
     */
    public boolean isTaskRunning(String taskId) {
        Future<?> runningTask = runningTasks.get(taskId);
        return runningTask != null && !runningTask.isDone();
    }

    /**
     * Get the number of currently running tasks
     */
    public int getRunningTaskCount() {
        // Clean up completed tasks
        runningTasks.entrySet().removeIf(entry -> entry.getValue().isDone());
        return runningTasks.size();
    }

    /**
     * Perform the actual document conversion
     */
    private ConversionResult performConversion(ConversionTask task) throws Exception {
        String filePath = task.getFilePath();
        String fileType = task.getFileType();
        String taskId = task.getTaskId();
        
        logger.debug("Converting file {} of type {} for task {}", filePath, fileType, taskId);
        
        // Update progress
        progressService.updateProgress(taskId, TaskStatus.PROCESSING, 10, "Initializing conversion", "Preparing");
        
        // Find appropriate processor
        SimpleProcessor processor = findProcessor(fileType);
        if (processor == null) {
            throw new ConversionException("No processor found for file type: " + fileType);
        }

        progressService.updateProgress(taskId, TaskStatus.PROCESSING, 20, "Found processor for: " + fileType, "Processing");
        
        try {
            // Read input file
            Path inputPath = Paths.get(filePath);
            if (!Files.exists(inputPath)) {
                throw new ConversionException("Input file not found: " + filePath);
            }
            
            progressService.updateProgress(taskId, TaskStatus.PROCESSING, 30, "Reading input file", "Reading");
            
            // Perform conversion based on file type
            String markdownContent = convertFile(processor, inputPath, taskId);
            
            progressService.updateProgress(taskId, TaskStatus.PROCESSING, 90, "Conversion completed", "Finalizing");
            
            // Generate result file name
            String resultFileName = task.getTargetFileName();
            if (resultFileName == null) {
                resultFileName = generateResultFileName(task.getFileName());
            }
            
            progressService.updateProgress(taskId, TaskStatus.PROCESSING, 95, "Preparing result", "Saving");
            
            return new ConversionResult(resultFileName, markdownContent);
            
        } catch (Exception e) {
            logger.error("Error during conversion for task {}: {}", taskId, e.getMessage());
            throw new ConversionException("Conversion error: " + e.getMessage(), e);
        }
    }

    /**
     * Convert file using the appropriate processor
     */
    private String convertFile(SimpleProcessor processor, Path inputPath, String taskId) throws Exception {
        // This is a simplified implementation
        // In a real implementation, you would need to adapt the existing processors
        // to work with the new async framework and progress tracking
        
        progressService.updateProgress(taskId, TaskStatus.PROCESSING, 40, "Starting document processing", "Converting");
        
        try {
            // For now, we'll simulate the conversion process
            // In the actual implementation, you would call the processor's process method
            
            // Simulate processing time and progress updates
            for (int i = 50; i <= 80; i += 10) {
                Thread.sleep(500); // Simulate processing time
                progressService.updateProgress(taskId, TaskStatus.PROCESSING, i, 
                    "Processing document... " + i + "%", "Converting");
                
                // Check for cancellation
                if (Thread.currentThread().isInterrupted()) {
                    throw new InterruptedException("Task was cancelled");
                }
            }
            
            // Read the file content (simplified)
            String content = Files.readString(inputPath);
            
            // For demonstration, we'll just wrap the content in markdown
            // In reality, each processor would handle its specific format
            String markdownContent = "# Converted Document\n\n" +
                "**Original file:** " + inputPath.getFileName() + "\n\n" +
                "**Conversion time:** " + java.time.LocalDateTime.now() + "\n\n" +
                "## Content\n\n" +
                content;
            
            progressService.updateProgress(taskId, TaskStatus.PROCESSING, 85, "Document processed successfully", "Post-processing");
            
            return markdownContent;
            
        } catch (InterruptedException e) {
            logger.info("Conversion cancelled for task {}", taskId);
            throw e;
        } catch (Exception e) {
            logger.error("Error processing document for task {}: {}", taskId, e.getMessage());
            throw new ConversionException("Document processing failed", e);
        }
    }

    /**
     * Find appropriate processor for file type
     */
    private SimpleProcessor findProcessor(String fileType) {
        // This is a simplified implementation
        // In reality, you would query the plugin manager for the appropriate processor

        try {
            // For now, we'll return a mock processor
            // In the actual implementation, you would use:
            // return pluginManager.getProcessorForFileType(fileType);

            return new SimpleProcessor(fileType);

        } catch (Exception e) {
            logger.error("Failed to find processor for file type {}: {}", fileType, e.getMessage());
            return null;
        }
    }

    /**
     * Generate result file name
     */
    private String generateResultFileName(String originalFileName) {
        String nameWithoutExtension = originalFileName.replaceFirst("[.][^.]+$", "");
        return nameWithoutExtension + ".md";
    }

    /**
     * Simple processor for demonstration
     */
    private static class SimpleProcessor {
        private final String fileType;

        public SimpleProcessor(String fileType) {
            this.fileType = fileType;
        }

        public String process(String input) throws Exception {
            // Mock implementation
            return "Processed content for " + fileType;
        }

        public boolean supports(String fileExtension) {
            return fileType.equalsIgnoreCase(fileExtension);
        }
    }

    /**
     * Result of a conversion operation
     */
    private static class ConversionResult {
        private final String fileName;
        private final String content;

        public ConversionResult(String fileName, String content) {
            this.fileName = fileName;
            this.content = content;
        }

        public String getFileName() { return fileName; }
        public String getContent() { return content; }
    }
}
