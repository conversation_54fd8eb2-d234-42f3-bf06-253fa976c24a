package com.talkweb.ai.indexer.web.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Configuration for API rate limiting
 */
@Configuration
@Profile("server")
public class RateLimitConfig {

    @Value("${doc-converter.web.security.rate-limit.enabled:true}")
    private boolean rateLimitEnabled;

    @Value("${doc-converter.web.security.rate-limit.requests-per-minute:60}")
    private int requestsPerMinute;

    @Value("${doc-converter.web.security.rate-limit.requests-per-hour:1000}")
    private int requestsPerHour;

    @Bean
    public RateLimitService rateLimitService() {
        return new RateLimitService(rateLimitEnabled, requestsPerMinute, requestsPerHour);
    }

    /**
     * Simple in-memory rate limiting service
     */
    public static class RateLimitService {
        
        private final boolean enabled;
        private final int requestsPerMinute;
        private final int requestsPerHour;
        
        // Track requests per client IP
        private final ConcurrentHashMap<String, ClientRateLimit> clientLimits = new ConcurrentHashMap<>();
        
        // Cleanup scheduler
        private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

        public RateLimitService(boolean enabled, int requestsPerMinute, int requestsPerHour) {
            this.enabled = enabled;
            this.requestsPerMinute = requestsPerMinute;
            this.requestsPerHour = requestsPerHour;
            
            // Schedule cleanup every 5 minutes
            scheduler.scheduleAtFixedRate(this::cleanup, 5, 5, TimeUnit.MINUTES);
        }

        /**
         * Check if request is allowed for the given client IP
         */
        public boolean isAllowed(String clientIp) {
            if (!enabled) {
                return true;
            }

            ClientRateLimit limit = clientLimits.computeIfAbsent(clientIp, k -> new ClientRateLimit());
            return limit.isAllowed(requestsPerMinute, requestsPerHour);
        }

        /**
         * Get remaining requests for the client
         */
        public RateLimitInfo getRateLimitInfo(String clientIp) {
            if (!enabled) {
                return new RateLimitInfo(requestsPerMinute, requestsPerHour, requestsPerMinute, requestsPerHour);
            }

            ClientRateLimit limit = clientLimits.get(clientIp);
            if (limit == null) {
                return new RateLimitInfo(requestsPerMinute, requestsPerHour, requestsPerMinute, requestsPerHour);
            }

            return limit.getRateLimitInfo(requestsPerMinute, requestsPerHour);
        }

        /**
         * Clean up old entries
         */
        private void cleanup() {
            long cutoffTime = System.currentTimeMillis() - TimeUnit.HOURS.toMillis(2);
            clientLimits.entrySet().removeIf(entry -> entry.getValue().getLastRequestTime() < cutoffTime);
        }

        /**
         * Rate limit tracking for a single client
         */
        private static class ClientRateLimit {
            private final AtomicInteger minuteCount = new AtomicInteger(0);
            private final AtomicInteger hourCount = new AtomicInteger(0);
            private volatile long lastMinuteReset = System.currentTimeMillis();
            private volatile long lastHourReset = System.currentTimeMillis();
            private volatile long lastRequestTime = System.currentTimeMillis();

            public synchronized boolean isAllowed(int maxPerMinute, int maxPerHour) {
                long now = System.currentTimeMillis();
                lastRequestTime = now;

                // Reset minute counter if needed
                if (now - lastMinuteReset >= TimeUnit.MINUTES.toMillis(1)) {
                    minuteCount.set(0);
                    lastMinuteReset = now;
                }

                // Reset hour counter if needed
                if (now - lastHourReset >= TimeUnit.HOURS.toMillis(1)) {
                    hourCount.set(0);
                    lastHourReset = now;
                }

                // Check limits
                if (minuteCount.get() >= maxPerMinute || hourCount.get() >= maxPerHour) {
                    return false;
                }

                // Increment counters
                minuteCount.incrementAndGet();
                hourCount.incrementAndGet();
                
                return true;
            }

            public RateLimitInfo getRateLimitInfo(int maxPerMinute, int maxPerHour) {
                long now = System.currentTimeMillis();

                // Reset counters if needed
                if (now - lastMinuteReset >= TimeUnit.MINUTES.toMillis(1)) {
                    minuteCount.set(0);
                }
                if (now - lastHourReset >= TimeUnit.HOURS.toMillis(1)) {
                    hourCount.set(0);
                }

                int remainingMinute = Math.max(0, maxPerMinute - minuteCount.get());
                int remainingHour = Math.max(0, maxPerHour - hourCount.get());

                return new RateLimitInfo(maxPerMinute, maxPerHour, remainingMinute, remainingHour);
            }

            public long getLastRequestTime() {
                return lastRequestTime;
            }
        }
    }

    /**
     * Rate limit information
     */
    public static class RateLimitInfo {
        private final int limitPerMinute;
        private final int limitPerHour;
        private final int remainingMinute;
        private final int remainingHour;

        public RateLimitInfo(int limitPerMinute, int limitPerHour, int remainingMinute, int remainingHour) {
            this.limitPerMinute = limitPerMinute;
            this.limitPerHour = limitPerHour;
            this.remainingMinute = remainingMinute;
            this.remainingHour = remainingHour;
        }

        // Getters
        public int getLimitPerMinute() { return limitPerMinute; }
        public int getLimitPerHour() { return limitPerHour; }
        public int getRemainingMinute() { return remainingMinute; }
        public int getRemainingHour() { return remainingHour; }
    }
}
